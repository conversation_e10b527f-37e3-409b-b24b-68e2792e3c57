{"name": "@beauty-crm/platform-appointment-infrastructure", "version": "1.0.0", "description": "Appointment Infrastructure Library - Repositories, controllers, and infrastructure logic extracted from appointment services", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "format": "biome format --write .", "lint": "biome lint .", "lint:fix": "biome lint --write .", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@beauty-crm/platform-appointment-schema": "workspace:*", "@beauty-crm/platform-appointment-domain": "workspace:*", "@beauty-crm/platform-appointment-eventing": "workspace:*", "@hono/zod-validator": "^0.7.0", "@prisma/client": "^6.11.1", "hono": "^4.8.3", "zod": "^3.25.73"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@types/node": "^22.15.34", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "keywords": ["appointment", "infrastructure", "repository", "controller", "beauty-crm"], "license": "MIT"}