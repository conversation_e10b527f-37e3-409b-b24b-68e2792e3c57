{"name": "@beauty-crm/platform-appointment-domain", "version": "1.0.0", "description": "Appointment Domain Logic Library - Business rules, aggregates, and domain services extracted from appointment services", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "format": "biome format --write .", "lint": "biome lint .", "lint:fix": "biome lint --write .", "test": "vitest", "test:coverage": "vitest --coverage"}, "dependencies": {"@beauty-crm/platform-appointment-schema": "workspace:*", "@beauty-crm/platform-eventing": "workspace:*", "@paralleldrive/cuid2": "^2.2.2", "zod": "^3.25.73"}, "devDependencies": {"@biomejs/biome": "^2.0.6", "@types/node": "^22.15.34", "typescript": "^5.8.3", "vitest": "^3.2.4"}, "keywords": ["appointment", "domain", "business-logic", "ddd", "aggregate", "beauty-crm"], "license": "MIT"}