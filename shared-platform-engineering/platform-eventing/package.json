{"author": "Beauty CRM Platform Team", "bugs": {"url": "https://github.com/odykyi/beauty-crm/issues"}, "dependencies": {"@prisma/client": "^6.11.1", "nats": "^2.28.2", "zod": "^3.25.67"}, "description": "Beautiful event-driven architecture with NATS JetStream - the eventing platform for Beauty CRM", "devDependencies": {"@biomejs/biome": "^2.0.0", "@types/node": "^22.15.29", "@vitest/coverage-v8": "^3.2.4", "typescript": "^5.8.3", "vitest": "^3.2.0"}, "files": ["dist", "README.md"], "homepage": "https://github.com/odykyi/beauty-crm/tree/main/shared-platform-engineering/platform-eventing#readme", "keywords": ["nats", "jetstream", "events", "messaging", "microservices", "beauty-crm", "eventing"], "license": "MIT", "main": "dist/index.js", "name": "@beauty-crm/platform-eventing", "repository": {"directory": "shared-platform-engineering/platform-eventing", "type": "git", "url": "git+https://github.com/odykyi/beauty-crm.git"}, "scripts": {"build": "bun run clean && tsc", "check": "biome check .", "clean": "rm -rf dist", "dev": "tsc --watch", "format": "biome format --write .", "lint": "biome lint .", "lint:fix": "biome lint --write .", "test": "vitest", "test:coverage": "vitest --coverage", "test:watch": "vitest --watch"}, "types": "dist/index.d.ts", "version": "1.0.0"}